[{"id": 1, "priority": 2, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}, {"header": "X-Extension-Request", "operation": "set", "value": "fillify-secure"}]}, "condition": {"urlFilter": "https://api.openai.com/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 2, "priority": 2, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}, {"header": "X-Extension-Request", "operation": "set", "value": "fillify-secure"}]}, "condition": {"urlFilter": "https://api.anthropic.com/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 3, "priority": 2, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}, {"header": "X-Extension-Request", "operation": "set", "value": "fillify-secure"}]}, "condition": {"urlFilter": "https://api.moonshot.cn/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 4, "priority": 2, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}, {"header": "X-Extension-Request", "operation": "set", "value": "fillify-secure"}]}, "condition": {"urlFilter": "https://api.deepseek.com/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 5, "priority": 2, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}, {"header": "X-Extension-Request", "operation": "set", "value": "fillify-secure"}]}, "condition": {"urlFilter": "https://generativelanguage.googleapis.com/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 6, "priority": 2, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}, {"header": "X-Extension-Request", "operation": "set", "value": "fillify-secure"}]}, "condition": {"urlFilter": "https://openrouter.ai/*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 7, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}]}, "condition": {"urlFilter": "http://localhost:*", "resourceTypes": ["xmlhttprequest"]}}, {"id": 8, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "Origin", "operation": "remove"}, {"header": "<PERSON><PERSON><PERSON>", "operation": "remove"}]}, "condition": {"urlFilter": "http://127.0.0.1:*", "resourceTypes": ["xmlhttprequest"]}}]