/**
 * 安全AI服务 - 多层隔离架构
 * 
 * 此服务实现了完全的请求隐藏机制：
 * 1. 所有AI请求和prompt构建逻辑都在background script中执行
 * 2. 使用declarativeNetRequest API修改请求头，确保请求由扩展发出
 * 3. 外部无法访问真实的prompt内容和API调用细节
 * 4. 提供简洁的接口，隐藏所有内部实现
 */

export interface SecureAiRequestOptions {
  description: string
  formFields: any
  mode: string
  language: string
  projectId?: string
}

export interface SecureAiResponse {
  success: boolean
  data?: {
    content: any
    usage?: any
  }
  error?: string
}

/**
 * 安全AI服务类
 * 提供完全隐藏的AI请求处理
 */
export class SecureAiService {
  /**
   * 发送安全AI请求
   * 所有敏感操作都在background script中完成
   * 
   * @param options 请求选项（只包含必要的用户输入）
   * @returns Promise<SecureAiResponse>
   */
  static async generateContent(options: SecureAiRequestOptions): Promise<SecureAiResponse> {
    try {
      // 通过消息传递发送到background script
      // background script将处理所有敏感操作：
      // - 获取API密钥
      // - 构建完整prompt（包含系统提示）
      // - 发起API请求
      // - 处理响应
      const response = await chrome.runtime.sendMessage({
        type: 'secureAiRequest',
        description: options.description,
        formFields: options.formFields,
        mode: options.mode,
        language: options.language,
        projectId: options.projectId
      })

      return response as SecureAiResponse
    } catch (error) {
      return {
        success: false,
        error: 'Service temporarily unavailable'
      }
    }
  }

  /**
   * 检查服务可用性
   * 不暴露任何内部状态信息
   */
  static async isAvailable(): Promise<boolean> {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'checkAiServiceStatus'
      })
      return response?.available === true
    } catch {
      return false
    }
  }
}

/**
 * 便捷函数 - 生成表单内容
 */
export async function generateFormContent(
  description: string,
  formFields: any,
  options: {
    mode?: string
    language?: string
    projectId?: string
  } = {}
): Promise<SecureAiResponse> {
  return SecureAiService.generateContent({
    description,
    formFields,
    mode: options.mode || 'form',
    language: options.language || 'auto',
    projectId: options.projectId
  })
}

/**
 * 便捷函数 - 生成邮件内容
 */
export async function generateEmailContent(
  description: string,
  formFields: any,
  options: {
    language?: string
    projectId?: string
  } = {}
): Promise<SecureAiResponse> {
  return SecureAiService.generateContent({
    description,
    formFields,
    mode: 'email',
    language: options.language || 'auto',
    projectId: options.projectId
  })
}

/**
 * 便捷函数 - 生成Bug报告内容
 */
export async function generateBugReportContent(
  description: string,
  formFields: any,
  options: {
    language?: string
    projectId?: string
  } = {}
): Promise<SecureAiResponse> {
  return SecureAiService.generateContent({
    description,
    formFields,
    mode: 'bugReport',
    language: options.language || 'auto',
    projectId: options.projectId
  })
}
