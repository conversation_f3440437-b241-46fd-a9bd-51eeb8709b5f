// Import reasoning model utilities
import { filterThinkingContent, isReasoningModel } from './reasoning-models';

// Form request interface
export interface FormRequest {
  description: string;
  formFields: string[];
  mode: string;
  language: string;
  project?: ProjectInfo;
  provider?: string;
  model?: string;
  apiKey?: string;
  useCustomApi?: boolean;
  userId?: string;
}

export interface ProjectInfo {
  name?: string;
  description?: string;
  environment?: string;
  template?: string;
}

// Language code mapping
const languageCodeMap: { [key: string]: string } = {
  'id': 'Bahasa Indonesia',
  'ms': 'Bahasa Melayu',
  'da': 'Dansk',
  'de': 'Deutsch',
  'en': 'English',
  'es': 'Español',
  'fr': 'Français',
  'it': 'Italiano',
  'nl': 'Nederlands',
  'no': 'Norsk',
  'pl': 'Polski',
  'pt': 'Português',
  'ro': 'Română',
  'fi': 'Suomi',
  'sv': 'Svenska',
  'vi': 'Tiếng Việt',
  'tr': 'Türkçe',
  'hu': 'Magyar',
  'cs': 'Če<PERSON><PERSON>',
  'uk': 'Українська',
  'ru': 'Русский',
  'bg': 'Български',
  'ar': 'العربية',
  'fa': 'فارسی',
  'he': 'עִבְרִית',
  'hi': 'हिन्दी',
  'th': 'ไทย',
  'ja': '日本語',
  'zh-CN': '中文（简体）',
  'zh-TW': '中文（繁體）',
  'ko': '한국어'
};

function getLanguageName(languageCode: string): string {
  return languageCodeMap[languageCode] || 'English';
}

function generateLanguageInstruction(request: FormRequest): string {
  if (request.language === 'auto') {
    return `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`;
  }

  const languageName = getLanguageName(request.language);
  return `Generate all content in ${languageName}`;
}

function generateBugReportPromptWithProject(request: FormRequest): string {
  // Only get language name when not 'auto'
  const language = request.language !== 'auto' ? getLanguageName(request.language) : null;
  const languageInstruction = request.language === 'auto'
    ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`
    : `Generate all content in ${language}`;

  const projectContext = `Project Information:
- Project Name: ${request.project?.name || 'N/A'}
- Project Description: ${request.project?.description || 'N/A'}
- Environment Details:
${request.project?.environment || 'N/A'}

IMPORTANT: You MUST use these exact environment details in your response. Do not modify or omit any environment information.

Project Context:
1. This is a ${request.project?.description} named "${request.project?.name}"
2. The environment information above MUST be included exactly as shown in the environment section of your response
3. All generated content should be relevant to this specific project
`;

  return `As an experienced QA engineer specializing in bug reporting, analyze the provided bug description and generate structured content for a bug tracking form in JSON format. ${languageInstruction}.

Context:
${projectContext}
Bug Description: "${request.description}"

Form Fields (Field Names and Types):
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. ${languageInstruction}
2. Return a valid JSON object with standardized field names for maximum compatibility:
   - Use "title" for bug title/summary
   - Use "description" for the main bug description (with template structure)
   - Use "body" as an alternative description field
   - Use "steps" for reproduction steps
   - Use "environment" for environment details
   - Use "priority" or "severity" for priority/severity
   - Use "status" for status fields
   - Also include any specific field names from the form if provided

3. CRITICAL - Content Structure:
   - The description field should ONLY contain the template structure below
   - Do NOT write any description text before the template
   - Put ALL description content INSIDE the template sections
   - Follow the template structure EXACTLY

4. Field-specific instructions:
   - **Title fields**: Generate a concise and descriptive bug title that includes the project name
   - **Description fields**:
     - Put the COMPLETE bug description ONLY in the template's sections
     - Do not write any description text outside the template structure
     - Include all details (description, expected/actual behavior, impact) within the template sections
   - **Steps fields**: List reproducible steps in a numbered format
   - **Environment fields**: Use EXACTLY the environment information provided in project context
   - **Priority/Severity fields**: Assign a level based on the provided impact assessment
   - **Status fields**: Default to "New" or "Open"

5. Template Structure:
${request.project?.template || `### Environment
${request.project?.environment}

### Description
Write a detailed description of the bug here, including:
- Detailed bug description
- Impact assessment and severity level

### Steps to Reproduce
1. First specific step
2. Second specific step
3. Additional steps as needed

### Expected behavior
Describe what should happen in ${request.project?.name}

### Actual behavior
Describe what actually happens

### Reproduces how often
Specify how frequently the issue occurs

### Additional Information
Add any other relevant context about ${request.project?.name}`}

6. Output Requirements:
   - JSON object only (no extra text)
   - Match field types (e.g., string, number) in "Form Fields"
   - Include project-specific details where relevant
   - Use clear and professional language in ${request.language || 'English'}
   - Ensure sensitive data is anonymized
   - DO NOT write any content outside the template structure
   - Put ALL description content INSIDE the template sections

Return only the JSON object. Do not include any additional text or explanation.`;
}

function generateBugReportPromptWithoutProject(request: FormRequest): string {
  // Only get language name when not 'auto'
  const language = request.language !== 'auto' ? getLanguageName(request.language) : null;
  const languageInstruction = request.language === 'auto'
    ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`
    : `Generate all content in ${language}`;

  return `As an experienced QA engineer specializing in bug reporting, analyze the provided bug description and generate structured content for a bug tracking form in JSON format. ${languageInstruction}.

Bug Description: "${request.description}"

Form Fields (Field Names and Types):
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. ${languageInstruction}
2. Return a valid JSON object with standardized field names for maximum compatibility:
   - Use "title" for bug title/summary
   - Use "description" for the main bug description (with template structure)
   - Use "body" as an alternative description field
   - Use "steps" for reproduction steps
   - Use "environment" for environment details
   - Use "priority" or "severity" for priority/severity
   - Use "status" for status fields
   - Also include any specific field names from the form if provided

3. Field-specific instructions:
   - **Title fields**: Generate a concise and descriptive bug title
   - **Description fields**:
     - Put the COMPLETE bug description ONLY in the template's "Description" section
     - Do not write any description text outside the template structure
     - Include all details (description, expected/actual behavior, impact) within the template sections
   - **Steps fields**: List reproducible steps in a numbered format
   - **Environment fields**: Extract relevant technical details from the description
   - **Priority/Severity fields**: Assign a level based on the provided impact assessment
   - **Status fields**: Default to "New" or "Open"

4. CRITICAL - Content Structure:
   - The description field should ONLY contain the template structure below
   - Do NOT write any description text before the template
   - Put ALL description content INSIDE the template sections
   - Follow the template structure EXACTLY

5. Template Structure:

### Environment
- Version:
- Operating System:
- Browser: Any/Chrome/Safari/Firefox/Edge/Safari for iOS/Chrome for Android/...
- Operating System: Any/Windows/macOS/Linux/ChromeOS/...

### Description
Write a detailed description of the bug here, including:
- Detailed bug description
- Impact assessment and severity level

### Steps to Reproduce
1. First specific step
2. Second specific step
3. Additional steps as needed

### Expected behavior
Describe what you expect to happen

### Actual behavior
Describe what actually happens

### Reproduces how often
Specify how frequently the issue occurs

### Additional Information
Add any other relevant context or information

6. Output Requirements:
   - JSON object only (no extra text)
   - Match field types (e.g., string, number) in "Form Fields"
   - Keep generated content concise but informative
   - Use clear and professional language in ${request.language || 'English'}
   - Ensure sensitive data is anonymized
   - DO NOT write any content outside the template structure

Return only the JSON object. Do not include any additional text or explanation.`;
}

export function generatePrompt(request: FormRequest): string {
  // Only get language name when not 'auto'
  const language = request.language !== 'auto' ? getLanguageName(request.language) : null;

  if (request.mode === 'bugReport') {
    return request.project
      ? generateBugReportPromptWithProject(request)
      : generateBugReportPromptWithoutProject(request);
  } else if (request.mode === 'email') {
    return `As a professional email writing assistant, generate an email based on the user's description. ${request.language === 'auto'
      ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language.`
      : `Generate all content in ${language}`}.

User Description: "${request.description}"

Form Fields:
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. Return a JSON object where keys match the field names/ids
2. Generate professional and appropriate email content
3. Keep the tone professional and courteous
4. Be concise but comprehensive

Return only the JSON object with no additional text.`;
  } else {
    return `As a form filling assistant, generate appropriate content for this form based on the user's description. ${request.language === 'auto'
      ? `First detect the language of this text: "${request.description}". Then generate ALL content using EXACTLY the same language as the detected language.`
      : `Generate all content in ${language}`}.

User Description: "${request.description}"

Form Fields:
${JSON.stringify(request.formFields, null, 2)}

Requirements:
1. Return a JSON object where keys match the field names/ids
2. Generate natural, context-appropriate content for each field
3. Respect the field types (text, email, number, etc.)
4. Keep generated content concise but informative

Return only the JSON object with no additional text.`;
  }
}

export function cleanAIResponse(content: string, provider?: string, model?: string): string {
  try {
    let cleanedContent = content;

    // Apply thinking content filtering for reasoning models
    if (provider && model && isReasoningModel(provider, model)) {
      console.log('Detected reasoning model, filtering thinking content...');
      cleanedContent = filterThinkingContent(content);
      console.log('Original content length:', content.length);
      console.log('Filtered content length:', cleanedContent.length);
    }

    // Standard JSON cleaning
    cleanedContent = cleanedContent.replace(/^```json\n?/, '').replace(/\n?```$/, '');

    const parsed = JSON.parse(cleanedContent);
    return JSON.stringify(parsed);
  } catch (error) {
    console.error('Failed to clean AI response:', error);
    console.error('Original content:', content);
    throw new Error('Invalid JSON response from AI provider');
  }
}


