// Function to detect and filter thinking content from reasoning models
export function filterThinkingContent(content: string): string {
  // Common patterns for thinking models
  const thinkingPatterns = [
    // DeepSeek-R1 style thinking
    /<think>[\s\S]*?<\/think>/gi,
    /<thinking>[\s\S]*?<\/thinking>/gi,
    
    // OpenAI o1 style thinking (often starts with specific phrases)
    /^[\s\S]*?(?=\{)/m, // Remove everything before the first JSON object
    
    // Common thinking indicators
    /^(?:Let me think|I need to|First, let me|Looking at this)[\s\S]*?(?=\{)/gmi,
    
    // Reasoning chains that end with "So the answer is" or similar
    /^[\s\S]*?(?:So the answer is|Therefore|In conclusion|The final answer is)[\s\S]*?(?=\{)/gmi,
    
    // Step-by-step reasoning
    /^(?:Step \d+|First|Second|Third|Finally)[\s\S]*?(?=\{)/gmi
  ];

  let cleanedContent = content;

  // Apply all thinking patterns
  for (const pattern of thinkingPatterns) {
    cleanedContent = cleanedContent.replace(pattern, '');
  }

  // Additional cleanup for common thinking artifacts
  cleanedContent = cleanedContent
    .replace(/^[\s\S]*?(?=\{)/m, '') // Remove everything before first {
    .replace(/\}[\s\S]*$/m, '}') // Remove everything after last }
    .trim();

  return cleanedContent;
}

// Function to detect if a model is a reasoning/thinking model
export function isReasoningModel(provider: string, model: string): boolean {
  const reasoningModels = [
    // DeepSeek reasoning models
    'deepseek-r1',
    'deepseek-reasoner',
    
    // OpenAI reasoning models
    'o1-preview',
    'o1-mini',
    'o1',
    
    // Other potential reasoning models
    'gpt-4o-reasoning',
    'claude-3-reasoning',
    
    // Generic patterns
    /.*-r1$/i,
    /.*-reasoning$/i,
    /.*-think$/i,
    /o1.*/i
  ];

  const modelLower = model.toLowerCase();
  
  return reasoningModels.some(pattern => {
    if (typeof pattern === 'string') {
      return modelLower.includes(pattern.toLowerCase());
    } else {
      return pattern.test(modelLower);
    }
  });
}
