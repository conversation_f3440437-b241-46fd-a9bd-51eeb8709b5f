# 思考模型处理测试指南

## 问题描述
思考模型（如DeepSeek-R1、OpenAI o1等）会在响应中包含大量的思考过程，这些内容不应该被填充到表单中，只应该保留最终的JSON结果。

## 支持的思考模型

### 自动检测的模型
系统会自动检测以下思考模型并过滤思考内容：

#### DeepSeek 思考模型
- `deepseek-r1`
- `deepseek-reasoner`
- 任何以 `-r1` 结尾的模型

#### OpenAI 思考模型
- `o1-preview`
- `o1-mini`
- `o1`
- 任何以 `o1` 开头的模型

#### 其他思考模型
- 任何包含 `-reasoning` 的模型
- 任何包含 `-think` 的模型

## 思考内容过滤机制

### 1. 标签过滤
系统会移除以下标签包围的思考内容：
```
<think>思考内容</think>
<thinking>思考内容</thinking>
```

### 2. 思考指示词过滤
移除以下开头的思考内容：
- "Let me think..."
- "I need to..."
- "First, let me..."
- "Looking at this..."

### 3. 推理链过滤
移除包含以下结束语的推理链：
- "So the answer is..."
- "Therefore..."
- "In conclusion..."
- "The final answer is..."

### 4. 步骤式推理过滤
移除以下格式的步骤式推理：
- "Step 1:", "Step 2:", etc.
- "First", "Second", "Third", "Finally"

### 5. JSON提取
- 自动提取第一个完整的JSON对象
- 移除JSON前后的所有文本
- 清理格式问题（如trailing commas）

## 测试步骤

### 准备工作
1. 确保使用支持的思考模型（如DeepSeek-R1）
2. 在扩展设置中配置相应的API key
3. 选择思考模型作为默认模型

### 测试场景

#### 场景1: Bug Report模式测试
1. 访问GitHub issue创建页面
2. 切换到Bug Report模式
3. 输入复杂的bug描述：
   ```
   我们的登录系统在高并发情况下出现了性能问题，用户登录时间超过30秒，
   数据库连接池可能存在死锁问题，需要详细分析和解决方案
   ```
4. 使用思考模型生成内容
5. 观察是否只填充了最终的JSON结果，没有思考过程

#### 场景2: 一般表单填充测试
1. 访问任何包含表单的网页
2. 使用一般模式
3. 输入需要分析的描述
4. 验证只填充了结果，没有思考内容

### 调试信息

#### 在浏览器控制台查看
打开开发者工具，查找以下调试信息：

```javascript
// 思考模型检测
"Detected reasoning model, filtering thinking content..."

// 内容长度对比
"Original content length: 2500"
"Filtered content length: 350"

// 各个provider的特定日志
"OpenAI: Detected reasoning model, filtering thinking content..."
"DeepSeek: Detected reasoning model, filtering thinking content..."
"OpenRouter: Detected reasoning model, filtering thinking content..."
```

### 预期行为

#### ✅ 正确行为
- 思考模型被自动检测
- 思考内容被完全过滤
- 只有最终的JSON结果被填充到表单
- 控制台显示内容长度显著减少

#### ❌ 错误行为
- 表单中出现思考过程文本
- 表单中出现 `<think>` 或 `<thinking>` 标签
- 表单中出现 "Let me think" 等思考指示词
- 表单中出现步骤式推理内容

### 示例对比

#### 原始思考模型响应
```
Let me think about this bug report step by step.

First, I need to analyze the login performance issue. The user mentioned that login takes over 30 seconds during high concurrency, which suggests a database connection pool deadlock problem.

<think>
This is a complex performance issue. I should structure the bug report to include:
1. Clear problem description
2. Environment details
3. Steps to reproduce
4. Expected vs actual behavior
5. Potential root cause analysis

The user mentioned high concurrency and database connection pool deadlock, so this is likely a backend infrastructure issue rather than a frontend problem.
</think>

So the answer is:
{
  "title": "Login Performance Issue - 30+ Second Delays During High Concurrency",
  "description": "### Environment\nProduction environment with high user load\n\n### Description\nLogin system experiencing severe performance degradation...",
  "priority": "High"
}
```

#### 过滤后的结果
```json
{
  "title": "Login Performance Issue - 30+ Second Delays During High Concurrency",
  "description": "### Environment\nProduction environment with high user load\n\n### Description\nLogin system experiencing severe performance degradation...",
  "priority": "High"
}
```

## 故障排除

### 如果思考内容仍然出现
1. **检查模型检测**: 确认控制台显示 "Detected reasoning model"
2. **检查模型名称**: 确认使用的模型名称在支持列表中
3. **检查过滤日志**: 查看内容长度是否有显著变化
4. **手动测试**: 在控制台手动调用 `filterThinkingContent()` 函数

### 常见问题及解决方案
1. **模型名称不匹配**: 添加新的思考模型到检测列表
2. **思考格式变化**: 更新过滤正则表达式
3. **JSON提取失败**: 改进JSON提取逻辑
4. **require错误**: ✅ 已修复 - 改用ES6 import语法，避免浏览器环境中的require错误

### 已修复的问题
- ✅ **ReferenceError: require is not defined**: 重构代码结构，使用ES6模块系统
- ✅ **循环导入问题**: 创建独立的reasoning-models.ts模块
- ✅ **浏览器兼容性**: 确保所有代码在浏览器环境中正常运行

## 扩展支持

### 添加新的思考模型
在 `isReasoningModel` 函数中添加新的模型名称或模式：

```typescript
const reasoningModels = [
  // 添加新的思考模型
  'new-thinking-model',
  'another-reasoning-model',
  /.*-think-v\d+$/i
];
```

### 添加新的思考模式
在 `filterThinkingContent` 函数中添加新的过滤模式：

```typescript
const thinkingPatterns = [
  // 添加新的思考模式
  /<new-think-tag>[\s\S]*?<\/new-think-tag>/gi,
  /^New thinking indicator[\s\S]*?(?=\{)/gmi
];
```

## 验证成功标准
- [ ] 思考模型被正确检测
- [ ] 思考内容被完全过滤
- [ ] 只有JSON结果被填充到表单
- [ ] 控制台显示正确的调试信息
- [ ] 非思考模型的行为不受影响
- [ ] 所有AI服务商都支持思考内容过滤
