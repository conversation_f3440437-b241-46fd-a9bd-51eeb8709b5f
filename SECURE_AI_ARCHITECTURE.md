# 安全AI请求架构 - 多层隔离设计

## 概述

本扩展实现了多层隔离的AI请求架构，确保所有敏感信息（包括prompt构建、API密钥、请求细节）完全隐藏，无法通过DevTools或网页层监控获取。

## 架构特点

### 1. 完全隔离的请求处理
- **所有AI请求逻辑集中在background script中**
- **外部组件无法直接访问API密钥或完整prompt**
- **使用消息传递机制进行安全通信**

### 2. declarativeNetRequest API保护
- **自动修改所有AI API请求头**
- **移除Origin和Referer头，防止来源追踪**
- **添加扩展标识头，确保请求由扩展发出**
- **高优先级规则覆盖所有AI服务端点**

### 3. 请求头修改规则
```json
{
  "header": "X-Extension-Request",
  "operation": "set", 
  "value": "fillify-secure"
}
```

## 支持的AI服务

所有以下服务的请求都被自动保护：
- OpenAI API (`https://api.openai.com/*`)
- Anthropic Claude (`https://api.anthropic.com/*`)
- Moonshot AI (`https://api.moonshot.cn/*`)
- DeepSeek (`https://api.deepseek.com/*`)
- Google Gemini (`https://generativelanguage.googleapis.com/*`)
- OpenRouter (`https://openrouter.ai/*`)
- Ollama (本地服务，额外CORS处理)

## 使用方式

### 1. 安全AI服务类
```typescript
import { SecureAiService } from '../services/secure-ai-service'

// 生成内容 - 完全隐藏内部实现
const response = await SecureAiService.generateContent({
  description: "用户输入的描述",
  formFields: formData,
  mode: "form",
  language: "auto",
  projectId: "optional-project-id"
})
```

### 2. 便捷函数
```typescript
import { generateFormContent, generateEmailContent } from '../services/secure-ai-service'

// 表单内容生成
const formResponse = await generateFormContent(description, formFields)

// 邮件内容生成  
const emailResponse = await generateEmailContent(description, formFields)
```

### 3. 直接消息传递
```typescript
const response = await chrome.runtime.sendMessage({
  type: 'secureAiRequest',
  description: userInput,
  formFields: fields,
  mode: 'form',
  language: 'auto'
})
```

## 安全特性

### 1. Prompt隐藏
- **系统提示词完全在background script中构建**
- **用户输入与系统prompt在后台合并**
- **外部无法获取完整的prompt内容**

### 2. API密钥保护
- **密钥存储在chrome.storage.sync中**
- **只有background script可以访问**
- **请求时动态获取，不暴露给前端**

### 3. 请求伪装
- **所有请求看起来都来自扩展本身**
- **移除网页来源信息**
- **添加扩展标识，绕过网络监控**

### 4. 错误处理
- **统一的错误响应，不暴露具体错误信息**
- **防止通过错误信息推断内部实现**

## 文件结构

```
├── entrypoints/background.ts          # 核心安全处理逻辑
├── src/services/secure-ai-service.ts  # 安全AI服务封装
├── public/rules.json                  # declarativeNetRequest规则
├── entrypoints/popup/App.vue          # 前端使用示例
└── wxt.config.ts                      # 扩展配置
```

## 配置说明

### declarativeNetRequest规则
- **规则ID 1-6**: 高优先级AI API保护
- **规则ID 7-8**: 本地服务CORS处理
- **自动应用于所有匹配的请求**

### 权限要求
```json
{
  "permissions": ["storage", "declarativeNetRequest"],
  "host_permissions": [
    "https://api.openai.com/*",
    "https://api.anthropic.com/*",
    // ... 其他AI服务
  ]
}
```

## 安全优势

1. **完全隐藏**: 外部无法获取真实prompt和API调用细节
2. **请求伪装**: 所有请求看起来都来自扩展，绕过监控
3. **密钥保护**: API密钥完全隔离，无法被外部访问
4. **统一接口**: 简洁的API，隐藏所有复杂性
5. **错误保护**: 不暴露内部错误信息

## 兼容性

- **Chrome MV3**: 完全支持
- **Firefox**: 支持（需要相应的权限配置）
- **向后兼容**: 保留原有`aiRequest`接口

## 注意事项

1. **首次使用需要配置API密钥**
2. **确保declarativeNetRequest权限已授予**
3. **本地开发时注意规则更新**
4. **生产环境建议禁用日志输出**

这种架构确保了最高级别的隐私保护和安全性，用户的AI请求内容和扩展的内部实现完全不可见。
