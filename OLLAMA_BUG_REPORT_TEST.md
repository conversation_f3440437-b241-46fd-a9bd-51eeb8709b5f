# Ollama Bug Report Mode Testing Guide

## Problem Description
Ollama在bug report模式下生成的内容无法填充到GitHub，而其他模式和其他AI服务商都正常工作。

## Root Cause Analysis
经过分析，问题可能出现在以下几个方面：

1. **JSON结构不匹配**: Ollama生成的JSON字段名与GitHub表单字段期望的名称不匹配
2. **JSON解析问题**: Ollama的响应格式可能包含额外的文本或格式，导致JSON解析失败
3. **字段映射逻辑**: GitHub的动态字段ID（如`:r1:`、`:r21:`）可能导致匹配失败

## 修复措施

### 1. 改进的JSON解析逻辑
- 更宽松的JSON提取和清理
- 更好的错误处理和fallback机制
- 移除trailing commas和其他格式问题

### 2. 标准化字段名生成
修改prompt以生成标准化的字段名：
- `title` - 用于bug标题
- `description` - 用于主要描述（包含模板结构）
- `body` - 作为描述的备选字段
- `steps` - 用于重现步骤
- `environment` - 用于环境信息
- `priority`/`severity` - 用于优先级
- `status` - 用于状态

### 3. 增强的Prompt指令
- 更明确的JSON格式要求
- 强调只返回JSON对象，不包含额外文本
- 重复的格式化指令确保Ollama理解要求

## 测试步骤

### 准备工作
1. 确保Ollama正在运行（`ollama serve`）
2. 在扩展设置中配置Ollama provider
3. 验证Ollama连接正常

### 测试场景

#### 场景1: GitHub Issue创建页面
1. 访问任何GitHub仓库的issue创建页面
2. 切换到Bug Report模式
3. 输入测试描述，例如：
   ```
   登录功能在Chrome浏览器中无法正常工作，点击登录按钮后页面没有响应
   ```
4. 点击生成按钮
5. 观察是否成功填充标题和描述字段

#### 场景2: 带项目上下文的Bug Report
1. 在设置中创建一个测试项目
2. 设置项目环境信息
3. 在bug report模式下选择该项目
4. 测试生成和填充

### 调试信息

#### 在浏览器控制台查看
1. 打开开发者工具 (F12)
2. 切换到Console标签
3. 查找以下调试信息：
   - `Ollama parsed content:` - 显示解析后的JSON内容
   - `Ollama content keys:` - 显示生成的字段名
   - `Available content keys for title:` - 显示可用的内容键
   - `Found title content using key:` - 显示匹配的字段

#### 预期的调试输出
```javascript
Ollama parsed content: {
  "title": "登录功能在Chrome浏览器中无法正常工作",
  "description": "### Environment\n...",
  "body": "### Environment\n...",
  "steps": "1. 打开Chrome浏览器\n2. 访问登录页面\n...",
  "environment": "Chrome 120, Windows 11",
  "priority": "High"
}

Ollama content keys: ["title", "description", "body", "steps", "environment", "priority"]

Available content keys for title: ["title", "description", "body", "steps", "environment", "priority"]
Found title content using key: title
```

### 故障排除

#### 如果仍然无法填充
1. **检查JSON格式**: 在控制台查看`Ollama raw response:`输出
2. **检查字段匹配**: 查看`Available content keys`是否包含期望的字段
3. **检查GitHub页面**: 确认页面已完全加载，表单字段可见

#### 常见问题
1. **Ollama返回非JSON格式**: 检查模型是否支持结构化输出
2. **字段名不匹配**: 查看调试输出中的字段名
3. **GitHub页面动态加载**: 等待页面完全加载后再尝试

## 验证成功标准
- [ ] Ollama在bug report模式下能成功生成JSON
- [ ] 生成的内容能正确填充到GitHub的标题字段
- [ ] 生成的内容能正确填充到GitHub的描述字段
- [ ] 生成的模板结构正确（包含Environment、Description、Steps等部分）
- [ ] 其他模式（general、email）仍然正常工作
- [ ] 其他AI服务商仍然正常工作

## 回滚方案
如果修复导致其他问题，可以：
1. 恢复到之前的Ollama处理逻辑
2. 或者为Ollama创建专门的处理分支

## 后续优化
1. 根据测试结果进一步优化JSON解析逻辑
2. 考虑为不同的AI服务商创建专门的响应处理器
3. 改进错误处理和用户反馈机制
