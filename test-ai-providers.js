// Simple test script to verify AI providers work correctly
// This is a basic test to ensure the providers can be imported and instantiated

const { createAIProvider } = require('./src/services/ai-providers.ts');
const { generatePrompt } = require('./src/services/prompt-generator.ts');

// Test data
const testFormRequest = {
  description: "Fill out a contact form with my information",
  formFields: ["name", "email", "message"],
  mode: "form",
  language: "en",
  provider: "openai",
  model: "gpt-3.5-turbo",
  apiKey: "test-key",
  useCustomApi: true
};

console.log('Testing AI Providers...');

try {
  // Test prompt generation
  console.log('1. Testing prompt generation...');
  const prompt = generatePrompt(testFormRequest);
  console.log('✓ Prompt generated successfully');
  console.log('Prompt length:', prompt.length);

  // Test provider creation
  console.log('2. Testing provider creation...');

  const providers = ['openai', 'claude', 'moonshot', 'gemini', 'deepseek', 'openrouter', 'ollama'];

  providers.forEach(provider => {
    try {
      if (provider === 'ollama') {
        // For Ollama, use endpoint as apiKey
        const aiProvider = createAIProvider(provider, 'http://localhost:11434', 'llama2');
        console.log(`✓ ${provider} provider created successfully`);
      } else {
        const aiProvider = createAIProvider(provider, 'test-key', 'test-model');
        console.log(`✓ ${provider} provider created successfully`);
      }
    } catch (error) {
      console.log(`✗ ${provider} provider failed:`, error.message);
    }
  });

  console.log('3. Testing bug report prompt...');
  const bugReportRequest = {
    ...testFormRequest,
    mode: 'bugReport',
    project: {
      name: 'Test Project',
      description: 'A test project',
      environment: 'Chrome 120, Windows 11',
      template: 'Default bug report template'
    }
  };
  
  const bugPrompt = generatePrompt(bugReportRequest);
  console.log('✓ Bug report prompt generated successfully');
  console.log('Bug prompt length:', bugPrompt.length);

  console.log('4. Testing email prompt...');
  const emailRequest = {
    ...testFormRequest,
    mode: 'email'
  };
  
  const emailPrompt = generatePrompt(emailRequest);
  console.log('✓ Email prompt generated successfully');
  console.log('Email prompt length:', emailPrompt.length);

  console.log('\n✅ All tests passed! AI providers are working correctly.');

} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
